import { useCallback, useRef } from 'react';
import { useNavigation } from '@react-navigation/native';
import LoadingView from '@src/components/Loading';
import { WebViewPlugin } from '@src/components/WebView/WebViewPlugin';
import { handleBridgeMessage } from '@tastien/rn-bridge/lib/native';
import { Linking, Platform, Text, View } from 'react-native';
import Config from 'react-native-config';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import RNWebView from 'react-native-webview';

interface MapWebViewOptimizedProps {
  url: string;
  onCallback?: (data: any) => void;
  onMapInteractionStart?: () => void;
  onMapInteractionEnd?: () => void;
}

const nativePlugin = new WebViewPlugin();

export const MapWebViewOptimized = ({
  url,
  onCallback,
  onMapInteractionStart,
  onMapInteractionEnd,
}: MapWebViewOptimizedProps) => {
  const navigation = useNavigation<TstOm.StackParams.ScreenNavigationProp['navigation']>();
  const panRef = useRef(null);

  const onMessage = useCallback(
    (event: { nativeEvent: { data: string } }) => {
      const { data } = event.nativeEvent;

      if (!data) {
        return;
      }

      // 添加 rn 与 h5 的通信
      handleBridgeMessage(data, nativePlugin.getWebViewRef, navigation);

      try {
        const msg = typeof data === 'string' && JSON.parse(data);

        if (!msg) {
          return;
        }

        onCallback?.(msg);

        if (msg?.openURL) {
          Linking.openURL(msg.openURL);
        }
      } catch (error) {
        console.log(error);
      }
    },
    [onCallback, navigation],
  );

  const onGestureEvent = useCallback(() => {
    // 手势开始时禁用父级滚动
    onMapInteractionStart?.();
  }, [onMapInteractionStart]);

  const onHandlerStateChange = useCallback(
    (event: any) => {
      if (event.nativeEvent.state === State.END || event.nativeEvent.state === State.CANCELLED) {
        // 手势结束时重新启用父级滚动
        onMapInteractionEnd?.();
      }
    },
    [onMapInteractionEnd],
  );

  return (
    <PanGestureHandler
      ref={panRef}
      onGestureEvent={onGestureEvent}
      onHandlerStateChange={onHandlerStateChange}
      shouldCancelWhenOutside={false}
      minPointers={1}
      maxPointers={2}
    >
      <View className="flex-1">
        <RNWebView
          ref={nativePlugin.getWebViewRef}
          startInLoadingState={true}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          sharedCookiesEnabled={true}
          webviewDebuggingEnabled={__DEV__}
          cacheEnabled={false}
          cacheMode="LOAD_NO_CACHE"
          contentMode="mobile"
          mixedContentMode="compatibility"
          androidLayerType="hardware"
          javaScriptEnabled={true}
          domStorageEnabled={true}
          allowsBackForwardNavigationGestures
          nestedScrollEnabled={false}
          scrollEnabled={false}
          bounces={false}
          originWhitelist={Platform.OS === 'ios' ? undefined : ['tel:*', 'https:*', 'http:*']}
          onMessage={onMessage}
          source={{
            uri: url,
            headers: {
              tag: Config.APP_TAG_GRAY,
            },
          }}
          renderLoading={() => (
            <View className="size-full items-center justify-center">
              <LoadingView />
            </View>
          )}
          renderError={(_, _errorCode: number, errorDesc: string) => (
            <View className="size-full items-center justify-center">
              <Text>{errorDesc}</Text>
              <Text>加载失败~</Text>
            </View>
          )}
        />
      </View>
    </PanGestureHandler>
  );
};
